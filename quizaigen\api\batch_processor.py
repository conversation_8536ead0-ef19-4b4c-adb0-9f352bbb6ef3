"""
Enhanced Batch Processor

This module handles batch processing of multiple inputs for question generation
with advanced parallelization, load balancing, and performance optimization.
"""

import time
import logging
from typing import List, Dict, Any, Optional, Union, Callable, Tuple
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor, as_completed, Future
from multiprocessing import cpu_count
from pathlib import Path
from enum import Enum
from dataclasses import dataclass

from ..generators import (
    MCQGenerator, BooleanGenerator, FAQGenerator,
    FillBlankGenerator, QuestionParaphraser, QuestionAnswerer
)
from ..inputs import TextProcessor, DocumentProcessor, URLProcessor
from ..core.exceptions import ProcessingError, ValidationError
from ..utils.performance_monitor import get_performance_monitor, monitor_performance


class TaskType(Enum):
    """Types of processing tasks for workload optimization."""
    IO_BOUND = "io_bound"      # File reading, URL fetching
    CPU_BOUND = "cpu_bound"    # Text processing, AI inference
    MIXED = "mixed"            # Combined I/O and CPU operations


class ProcessingMode(Enum):
    """Processing execution modes."""
    SEQUENTIAL = "sequential"
    THREADING = "threading"
    MULTIPROCESSING = "multiprocessing"
    ADAPTIVE = "adaptive"


@dataclass
class WorkloadMetrics:
    """Metrics for workload analysis and optimization."""
    total_inputs: int
    estimated_cpu_time: float
    estimated_io_time: float
    memory_requirement: float
    recommended_mode: ProcessingMode
    optimal_workers: int


@dataclass
class ProcessingResult:
    """Enhanced result structure for batch processing."""
    success: bool
    input_data: Dict[str, Any]
    questions: Dict[str, List[Dict]] = None
    total_questions: int = 0
    processing_time: float = 0.0
    memory_used: float = 0.0
    worker_id: Optional[str] = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = None


class EnhancedBatchProcessor:
    """
    Enhanced batch processor with advanced parallelization and optimization.

    Features:
    - Adaptive processing mode selection (threading vs multiprocessing)
    - Workload analysis and optimization
    - Memory-aware processing
    - Performance monitoring and metrics
    - Load balancing and resource management
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize Enhanced BatchProcessor.

        Args:
            config: Configuration dictionary with enhanced options
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)

        # Initialize processors
        self.text_processor = TextProcessor(config)
        self.document_processor = DocumentProcessor(config)
        self.url_processor = URLProcessor(config)

        # Initialize generators
        self.generators = {
            'mcq': MCQGenerator(config),
            'boolean': BooleanGenerator(config),
            'faq': FAQGenerator(config),
            'fill_blank': FillBlankGenerator(config),
        }

        # Initialize other components
        self.paraphraser = QuestionParaphraser(config)
        self.answerer = QuestionAnswerer(config)

        # Enhanced processing settings
        self._setup_processing_config()

        # Performance monitoring
        self.performance_metrics = {
            'total_batches_processed': 0,
            'total_processing_time': 0.0,
            'average_throughput': 0.0,
            'memory_usage_history': [],
            'mode_usage_stats': {mode.value: 0 for mode in ProcessingMode}
        }

        self.logger.info(f"Initialized Enhanced BatchProcessor with {self.max_workers} max workers")

    def _setup_processing_config(self):
        """Setup enhanced processing configuration."""
        # System resource detection
        self.cpu_count = cpu_count()
        self.available_memory = self._get_available_memory()

        # Processing configuration
        self.max_workers = self.config.get('max_workers', min(self.cpu_count, 8))
        self.timeout_per_item = self.config.get('timeout_per_item', 300)
        self.continue_on_error = self.config.get('continue_on_error', True)

        # Advanced settings
        self.adaptive_mode = self.config.get('adaptive_mode', True)
        self.memory_limit_mb = self.config.get('memory_limit_mb', self.available_memory * 0.8)
        self.enable_performance_monitoring = self.config.get('enable_performance_monitoring', True)
        self.chunk_size = self.config.get('chunk_size', 'auto')  # Auto-calculate or fixed size

        # Processing mode preferences
        self.preferred_mode = ProcessingMode(
            self.config.get('preferred_mode', ProcessingMode.ADAPTIVE.value)
        )

        # Workload thresholds
        self.cpu_bound_threshold = self.config.get('cpu_bound_threshold', 0.7)
        self.io_bound_threshold = self.config.get('io_bound_threshold', 0.3)

    def _get_available_memory(self) -> float:
        """Get available system memory in MB."""
        try:
            import psutil
            return psutil.virtual_memory().available / (1024 * 1024)
        except ImportError:
            # Fallback estimation
            return 4096  # 4GB default assumption
    
    @monitor_performance("batch_processing")
    def process_batch(self, inputs: List[Dict[str, Any]],
                     question_types: List[str] = None,
                     num_questions_per_type: int = 5,
                     **kwargs) -> Dict[str, Any]:
        """
        Enhanced batch processing with adaptive optimization.

        Args:
            inputs: List of input dictionaries with 'type' and 'content' keys
            question_types: List of question types to generate
            num_questions_per_type: Number of questions per type
            **kwargs: Additional processing options:
                - force_mode: Override adaptive mode selection
                - chunk_size: Override automatic chunk sizing
                - enable_monitoring: Enable detailed performance monitoring

        Returns:
            Enhanced dictionary with batch processing results and metrics
        """
        if not inputs:
            raise ValueError("No inputs provided for batch processing")

        question_types = question_types or ['mcq', 'boolean', 'faq']

        self.logger.info(f"Starting enhanced batch processing of {len(inputs)} inputs")

        start_time = time.time()

        # Get performance monitor for adaptive optimization
        monitor = get_performance_monitor()

        # Analyze workload and determine optimal processing strategy
        workload_metrics = self._analyze_workload(inputs, question_types, num_questions_per_type)

        # Get system recommendations for optimization
        system_recommendations = monitor.get_system_recommendations()
        optimal_workers = monitor.optimize_worker_count(
            workload_size=len(inputs),
            operation_type=workload_metrics.recommended_mode.value
        )
        processing_mode = kwargs.get('force_mode') or workload_metrics.recommended_mode

        self.logger.info(f"Using {processing_mode.value} mode with {workload_metrics.optimal_workers} workers")

        # Initialize enhanced results structure
        results = {
            'inputs_processed': 0,
            'inputs_failed': 0,
            'total_questions_generated': 0,
            'results': [],
            'errors': [],
            'processing_time': 0,
            'workload_metrics': workload_metrics.__dict__,
            'processing_mode': processing_mode.value,
            'performance_metrics': {},
            'summary': {}
        }

        # Execute processing based on determined mode
        try:
            if processing_mode == ProcessingMode.SEQUENTIAL:
                results = self._process_batch_sequential(
                    inputs, question_types, num_questions_per_type, **kwargs
                )
            elif processing_mode == ProcessingMode.THREADING:
                results = self._process_batch_threaded(
                    inputs, question_types, num_questions_per_type,
                    workload_metrics.optimal_workers, **kwargs
                )
            elif processing_mode == ProcessingMode.MULTIPROCESSING:
                results = self._process_batch_multiprocess(
                    inputs, question_types, num_questions_per_type,
                    workload_metrics.optimal_workers, **kwargs
                )
            else:  # ADAPTIVE
                results = self._process_batch_adaptive(
                    inputs, question_types, num_questions_per_type,
                    workload_metrics, **kwargs
                )

        except Exception as e:
            self.logger.error(f"Batch processing failed: {str(e)}")
            results['errors'].append({'error': str(e), 'stage': 'batch_processing'})

        # Calculate final metrics and performance data
        processing_time = time.time() - start_time
        results['processing_time'] = processing_time
        results['performance_metrics'] = self._calculate_performance_metrics(
            results, processing_time, workload_metrics
        )
        results['summary'] = self._generate_enhanced_summary(results)

        # Update global performance tracking
        self._update_performance_history(results, processing_mode)

        self.logger.info(
            f"Enhanced batch processing completed in {processing_time:.2f}s "
            f"({results['inputs_processed']}/{len(inputs)} successful)"
        )

        return results

    def _analyze_workload(self, inputs: List[Dict[str, Any]],
                         question_types: List[str],
                         num_questions_per_type: int) -> WorkloadMetrics:
        """
        Analyze workload characteristics to determine optimal processing strategy.

        Args:
            inputs: Input data list
            question_types: Question types to generate
            num_questions_per_type: Questions per type

        Returns:
            Workload analysis metrics
        """
        total_inputs = len(inputs)

        # Analyze input types and estimate processing requirements
        io_operations = 0
        cpu_operations = 0
        estimated_memory = 0

        for input_data in inputs:
            input_type = input_data.get('type', '').lower()
            content = input_data.get('content', '')

            if input_type == 'file':
                io_operations += 1
                # Estimate file size impact
                try:
                    file_size = Path(content).stat().st_size / (1024 * 1024)  # MB
                    estimated_memory += file_size * 2  # Processing overhead
                except:
                    estimated_memory += 10  # Default assumption

            elif input_type == 'url':
                io_operations += 1
                estimated_memory += 5  # Average web page size

            elif input_type == 'text':
                cpu_operations += 1
                text_size = len(content) / (1024 * 1024)  # MB
                estimated_memory += text_size * 1.5

        # Estimate processing times
        estimated_io_time = io_operations * 2.0  # 2 seconds per I/O operation
        estimated_cpu_time = (cpu_operations + total_inputs) * len(question_types) * 0.5

        # Calculate workload ratios
        total_operations = io_operations + cpu_operations + total_inputs
        io_ratio = io_operations / total_operations if total_operations > 0 else 0
        cpu_ratio = (cpu_operations + total_inputs) / total_operations if total_operations > 0 else 1

        # Determine optimal processing mode
        if total_inputs <= 2:
            recommended_mode = ProcessingMode.SEQUENTIAL
            optimal_workers = 1
        elif io_ratio > self.io_bound_threshold:
            recommended_mode = ProcessingMode.THREADING
            optimal_workers = min(self.max_workers, total_inputs, 16)  # I/O can handle more threads
        elif cpu_ratio > self.cpu_bound_threshold:
            recommended_mode = ProcessingMode.MULTIPROCESSING
            optimal_workers = min(self.max_workers, self.cpu_count, total_inputs)
        else:
            recommended_mode = ProcessingMode.ADAPTIVE
            optimal_workers = min(self.max_workers, total_inputs)

        # Memory constraint check
        if estimated_memory > self.memory_limit_mb:
            self.logger.warning(f"Estimated memory usage ({estimated_memory:.1f}MB) exceeds limit")
            # Reduce workers to manage memory
            optimal_workers = max(1, optimal_workers // 2)
            if recommended_mode == ProcessingMode.MULTIPROCESSING:
                recommended_mode = ProcessingMode.THREADING

        return WorkloadMetrics(
            total_inputs=total_inputs,
            estimated_cpu_time=estimated_cpu_time,
            estimated_io_time=estimated_io_time,
            memory_requirement=estimated_memory,
            recommended_mode=recommended_mode,
            optimal_workers=optimal_workers
        )

    def _process_batch_threaded(self, inputs: List[Dict[str, Any]],
                               question_types: List[str],
                               num_questions_per_type: int,
                               num_workers: int,
                               **kwargs) -> Dict[str, Any]:
        """
        Process inputs using ThreadPoolExecutor with enhanced error handling and monitoring.

        Args:
            inputs: Input data list
            question_types: Question types to generate
            num_questions_per_type: Questions per type
            num_workers: Number of worker threads
            **kwargs: Additional options

        Returns:
            Processing results
        """
        results = {
            'inputs_processed': 0,
            'inputs_failed': 0,
            'total_questions_generated': 0,
            'results': [None] * len(inputs),
            'errors': [],
            'worker_stats': {}
        }

        with ThreadPoolExecutor(max_workers=num_workers, thread_name_prefix="QuizAI") as executor:
            # Submit all tasks with enhanced monitoring
            future_to_index = {}
            for i, input_data in enumerate(inputs):
                future = executor.submit(
                    self._process_single_input_enhanced,
                    input_data, question_types, num_questions_per_type,
                    f"thread-{i % num_workers}", **kwargs
                )
                future_to_index[future] = i

            # Collect results with timeout and progress tracking
            completed_count = 0
            for future in as_completed(future_to_index, timeout=self.timeout_per_item * len(inputs)):
                index = future_to_index[future]
                completed_count += 1

                try:
                    result = future.result()

                    if result.success:
                        results['inputs_processed'] += 1
                        results['total_questions_generated'] += result.total_questions
                    else:
                        results['inputs_failed'] += 1
                        results['errors'].append({
                            'input_index': index,
                            'error': result.error,
                            'worker_id': result.worker_id
                        })

                    results['results'][index] = result.__dict__

                    # Update worker statistics
                    worker_id = result.worker_id
                    if worker_id not in results['worker_stats']:
                        results['worker_stats'][worker_id] = {
                            'tasks_completed': 0,
                            'total_time': 0.0,
                            'total_questions': 0
                        }

                    results['worker_stats'][worker_id]['tasks_completed'] += 1
                    results['worker_stats'][worker_id]['total_time'] += result.processing_time
                    results['worker_stats'][worker_id]['total_questions'] += result.total_questions

                    # Progress logging
                    if completed_count % max(1, len(inputs) // 10) == 0:
                        progress = (completed_count / len(inputs)) * 100
                        self.logger.info(f"Threading progress: {progress:.1f}% ({completed_count}/{len(inputs)})")

                except Exception as e:
                    self.logger.error(f"Error processing input {index} in thread: {str(e)}")
                    results['inputs_failed'] += 1
                    results['errors'].append({
                        'input_index': index,
                        'error': str(e),
                        'stage': 'thread_execution'
                    })

                    results['results'][index] = ProcessingResult(
                        success=False,
                        input_data=inputs[index],
                        error=str(e)
                    ).__dict__

        return results

    def _process_batch_multiprocess(self, inputs: List[Dict[str, Any]],
                                   question_types: List[str],
                                   num_questions_per_type: int,
                                   num_workers: int,
                                   **kwargs) -> Dict[str, Any]:
        """
        Process inputs using ProcessPoolExecutor for CPU-intensive tasks.

        Args:
            inputs: Input data list
            question_types: Question types to generate
            num_questions_per_type: Questions per type
            num_workers: Number of worker processes
            **kwargs: Additional options

        Returns:
            Processing results
        """
        results = {
            'inputs_processed': 0,
            'inputs_failed': 0,
            'total_questions_generated': 0,
            'results': [None] * len(inputs),
            'errors': [],
            'worker_stats': {}
        }

        # Prepare serializable data for multiprocessing
        serializable_config = self._prepare_serializable_config()

        with ProcessPoolExecutor(max_workers=num_workers) as executor:
            # Submit all tasks
            future_to_index = {}
            for i, input_data in enumerate(inputs):
                future = executor.submit(
                    _process_single_input_multiprocess,  # Static function for pickling
                    input_data, question_types, num_questions_per_type,
                    serializable_config, f"process-{i % num_workers}", **kwargs
                )
                future_to_index[future] = i

            # Collect results
            completed_count = 0
            for future in as_completed(future_to_index, timeout=self.timeout_per_item * len(inputs)):
                index = future_to_index[future]
                completed_count += 1

                try:
                    result_dict = future.result()
                    result = ProcessingResult(**result_dict)

                    if result.success:
                        results['inputs_processed'] += 1
                        results['total_questions_generated'] += result.total_questions
                    else:
                        results['inputs_failed'] += 1
                        results['errors'].append({
                            'input_index': index,
                            'error': result.error,
                            'worker_id': result.worker_id
                        })

                    results['results'][index] = result.__dict__

                    # Progress logging
                    if completed_count % max(1, len(inputs) // 10) == 0:
                        progress = (completed_count / len(inputs)) * 100
                        self.logger.info(f"Multiprocessing progress: {progress:.1f}% ({completed_count}/{len(inputs)})")

                except Exception as e:
                    self.logger.error(f"Error processing input {index} in process: {str(e)}")
                    results['inputs_failed'] += 1
                    results['errors'].append({
                        'input_index': index,
                        'error': str(e),
                        'stage': 'process_execution'
                    })

                    results['results'][index] = ProcessingResult(
                        success=False,
                        input_data=inputs[index],
                        error=str(e)
                    ).__dict__

        return results

    def _prepare_serializable_config(self) -> Dict[str, Any]:
        """Prepare configuration that can be serialized for multiprocessing."""
        return {
            'timeout_per_item': self.timeout_per_item,
            'continue_on_error': self.continue_on_error,
            # Add other serializable config items
        }

    def _process_single_input_enhanced(self, input_data: Dict[str, Any],
                                     question_types: List[str],
                                     num_questions_per_type: int,
                                     worker_id: str,
                                     **kwargs) -> ProcessingResult:
        """
        Enhanced single input processing with detailed monitoring and error handling.

        Args:
            input_data: Input data dictionary
            question_types: Question types to generate
            num_questions_per_type: Questions per type
            worker_id: Worker identifier for tracking
            **kwargs: Additional options

        Returns:
            Enhanced processing result
        """
        start_time = time.time()
        memory_start = self._get_memory_usage()

        try:
            # Extract text from input
            text = self._extract_text_from_input(input_data)

            if not text or len(text.strip()) < 50:
                return ProcessingResult(
                    success=False,
                    input_data=input_data,
                    error='Insufficient text content',
                    processing_time=time.time() - start_time,
                    worker_id=worker_id
                )

            # Generate questions for each type with parallel processing within types
            all_questions = {}
            total_questions = 0
            generation_times = {}

            for question_type in question_types:
                if question_type not in self.generators:
                    self.logger.warning(f"Unknown question type: {question_type}")
                    continue

                try:
                    type_start_time = time.time()
                    generator = self.generators[question_type]
                    questions = generator.generate_questions(
                        text, num_questions_per_type, **kwargs
                    )

                    all_questions[question_type] = [q.to_dict() for q in questions]
                    total_questions += len(questions)
                    generation_times[question_type] = time.time() - type_start_time

                except Exception as e:
                    self.logger.error(f"Error generating {question_type} questions: {str(e)}")
                    all_questions[question_type] = []
                    generation_times[question_type] = 0

            processing_time = time.time() - start_time
            memory_used = self._get_memory_usage() - memory_start

            return ProcessingResult(
                success=True,
                input_data=input_data,
                questions=all_questions,
                total_questions=total_questions,
                processing_time=processing_time,
                memory_used=memory_used,
                worker_id=worker_id,
                metadata={
                    'text_length': len(text),
                    'question_types_generated': list(all_questions.keys()),
                    'generation_times': generation_times,
                    'average_time_per_question': processing_time / total_questions if total_questions > 0 else 0
                }
            )

        except Exception as e:
            self.logger.error(f"Error processing input in {worker_id}: {str(e)}")
            return ProcessingResult(
                success=False,
                input_data=input_data,
                error=str(e),
                processing_time=time.time() - start_time,
                worker_id=worker_id
            )

    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / (1024 * 1024)
        except ImportError:
            return 0.0

    def _process_batch_adaptive(self, inputs: List[Dict[str, Any]],
                               question_types: List[str],
                               num_questions_per_type: int,
                               workload_metrics: WorkloadMetrics,
                               **kwargs) -> Dict[str, Any]:
        """
        Adaptive processing that switches between threading and multiprocessing based on workload.
        """
        # For small batches, use threading
        if len(inputs) <= 5:
            return self._process_batch_threaded(
                inputs, question_types, num_questions_per_type,
                workload_metrics.optimal_workers, **kwargs
            )

        # For mixed workloads, chunk and use appropriate method for each chunk
        io_inputs = []
        cpu_inputs = []

        for i, input_data in enumerate(inputs):
            input_type = input_data.get('type', '').lower()
            if input_type in ['file', 'url']:
                io_inputs.append((i, input_data))
            else:
                cpu_inputs.append((i, input_data))

        results = {
            'inputs_processed': 0,
            'inputs_failed': 0,
            'total_questions_generated': 0,
            'results': [None] * len(inputs),
            'errors': []
        }

        # Process I/O-bound tasks with threading
        if io_inputs:
            io_data = [data for _, data in io_inputs]
            io_results = self._process_batch_threaded(
                io_data, question_types, num_questions_per_type,
                min(len(io_inputs), 8), **kwargs
            )

            # Map results back to original indices
            for j, (original_idx, _) in enumerate(io_inputs):
                if j < len(io_results['results']):
                    results['results'][original_idx] = io_results['results'][j]

            results['inputs_processed'] += io_results['inputs_processed']
            results['inputs_failed'] += io_results['inputs_failed']
            results['total_questions_generated'] += io_results['total_questions_generated']
            results['errors'].extend(io_results['errors'])

        # Process CPU-bound tasks with multiprocessing
        if cpu_inputs:
            cpu_data = [data for _, data in cpu_inputs]
            cpu_results = self._process_batch_multiprocess(
                cpu_data, question_types, num_questions_per_type,
                min(len(cpu_inputs), self.cpu_count), **kwargs
            )

            # Map results back to original indices
            for j, (original_idx, _) in enumerate(cpu_inputs):
                if j < len(cpu_results['results']):
                    results['results'][original_idx] = cpu_results['results'][j]

            results['inputs_processed'] += cpu_results['inputs_processed']
            results['inputs_failed'] += cpu_results['inputs_failed']
            results['total_questions_generated'] += cpu_results['total_questions_generated']
            results['errors'].extend(cpu_results['errors'])

        return results

    def _process_batch_sequential(self, inputs: List[Dict[str, Any]],
                                question_types: List[str],
                                num_questions_per_type: int,
                                **kwargs) -> Dict[str, Any]:
        """Process inputs sequentially (enhanced version)."""
        results = {
            'inputs_processed': 0,
            'inputs_failed': 0,
            'total_questions_generated': 0,
            'results': [],
            'errors': []
        }

        for i, input_data in enumerate(inputs):
            try:
                self.logger.info(f"Processing input {i+1}/{len(inputs)}")

                result = self._process_single_input_enhanced(
                    input_data, question_types, num_questions_per_type, f"sequential-{i}", **kwargs
                )

                if result.success:
                    results['inputs_processed'] += 1
                    results['total_questions_generated'] += result.total_questions
                else:
                    results['inputs_failed'] += 1
                    results['errors'].append({
                        'input_index': i,
                        'error': result.error
                    })

                results['results'].append(result.__dict__)

            except Exception as e:
                self.logger.error(f"Error processing input {i}: {str(e)}")
                results['inputs_failed'] += 1
                results['errors'].append({
                    'input_index': i,
                    'error': str(e)
                })

                if not self.continue_on_error:
                    break

        return results


# Static function for multiprocessing (must be at module level for pickling)
def _process_single_input_multiprocess(input_data: Dict[str, Any],
                                     question_types: List[str],
                                     num_questions_per_type: int,
                                     config: Dict[str, Any],
                                     worker_id: str,
                                     **kwargs) -> Dict[str, Any]:
    """
    Static function for multiprocessing - creates fresh instances to avoid pickling issues.

    Args:
        input_data: Input data dictionary
        question_types: Question types to generate
        num_questions_per_type: Questions per type
        config: Serializable configuration
        worker_id: Worker identifier
        **kwargs: Additional options

    Returns:
        Processing result as dictionary
    """
    try:
        # Create fresh processor instances for this process
        from ..generators import MCQGenerator, BooleanGenerator, FAQGenerator, FillBlankGenerator
        from ..inputs import TextProcessor, DocumentProcessor, URLProcessor

        generators = {
            'mcq': MCQGenerator(config),
            'boolean': BooleanGenerator(config),
            'faq': FAQGenerator(config),
            'fill_blank': FillBlankGenerator(config),
        }

        text_processor = TextProcessor(config)
        document_processor = DocumentProcessor(config)
        url_processor = URLProcessor(config)

        start_time = time.time()

        # Extract text from input
        input_type = input_data.get('type', '').lower()
        content = input_data.get('content', '')

        if input_type == 'text':
            text = content
        elif input_type == 'file':
            result = document_processor.process(content)
            if not result['success']:
                raise ProcessingError(f"Failed to process file: {result.get('error', 'Unknown error')}")
            text = result['text']
        elif input_type == 'url':
            result = url_processor.process(content)
            if not result['success']:
                raise ProcessingError(f"Failed to process URL: {result.get('error', 'Unknown error')}")
            text = result['text']
        else:
            raise ValueError(f"Unsupported input type: {input_type}")

        if not text or len(text.strip()) < 50:
            return ProcessingResult(
                success=False,
                input_data=input_data,
                error='Insufficient text content',
                processing_time=time.time() - start_time,
                worker_id=worker_id
            ).__dict__

        # Generate questions
        all_questions = {}
        total_questions = 0

        for question_type in question_types:
            if question_type not in generators:
                continue

            try:
                generator = generators[question_type]
                questions = generator.generate_questions(text, num_questions_per_type, **kwargs)
                all_questions[question_type] = [q.to_dict() for q in questions]
                total_questions += len(questions)
            except Exception as e:
                all_questions[question_type] = []

        return ProcessingResult(
            success=True,
            input_data=input_data,
            questions=all_questions,
            total_questions=total_questions,
            processing_time=time.time() - start_time,
            worker_id=worker_id,
            metadata={'text_length': len(text)}
        ).__dict__

    except Exception as e:
        return ProcessingResult(
            success=False,
            input_data=input_data,
            error=str(e),
            processing_time=time.time() - start_time if 'start_time' in locals() else 0,
            worker_id=worker_id
        ).__dict__


# Maintain backward compatibility
class BatchProcessor(EnhancedBatchProcessor):
    """Backward compatible BatchProcessor with enhanced capabilities."""
    pass

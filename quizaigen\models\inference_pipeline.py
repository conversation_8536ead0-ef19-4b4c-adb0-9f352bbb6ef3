"""
Inference Pipeline for QuizAIGen

Provides a unified interface for running AI model inference across different tiers.
Handles model selection, fallback strategies, and result aggregation.
"""

from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
import time

from .model_manager import ModelManager, ModelType
from .base_model import ModelTier, ModelOutput
from ..generators.base import Question
from ..core.exceptions import ProcessingError, LicenseError
from ..utils.logger import LoggerMixin


@dataclass
class InferenceRequest:
    """Request for AI model inference."""
    task: str  # 'enhance_question', 'validate_answer', 'generate_fill_blank', etc.
    input_data: Dict[str, Any]
    tier: ModelTier
    options: Dict[str, Any] = None


@dataclass
class InferenceResult:
    """Result of AI model inference."""
    success: bool
    output: Any
    confidence: float
    processing_time: float
    model_used: str
    tier: ModelTier
    metadata: Dict[str, Any] = None
    error: Optional[str] = None


class InferencePipeline(LoggerMixin):
    """
    Unified inference pipeline for AI models.
    
    Provides a single interface for all AI model operations with:
    - Automatic model selection based on tier
    - Fallback strategies for failed operations
    - Result caching and optimization
    - Batch processing capabilities
    """
    
    def __init__(self,
                 tier: ModelTier = ModelTier.FREE,
                 cache_dir: Optional[str] = None,
                 enable_fallback: bool = True):
        """
        Initialize inference pipeline.

        Args:
            tier: Current license tier
            cache_dir: Directory for model caching
            enable_fallback: Whether to enable fallback to lower-tier models
        """
        super().__init__()  # Initialize LoggerMixin
        self.tier = tier
        self.enable_fallback = enable_fallback
        
        # Initialize model manager
        self.model_manager = ModelManager(
            tier=tier,
            cache_dir=cache_dir,
            enable_model_cache=True
        )
        
        # Task to model mapping
        self.task_models = {
            "enhance_question": [ModelType.QUALITY_ENHANCER, ModelType.T5_GENERATOR],
            "validate_answer": [ModelType.BERT_VALIDATOR],
            "generate_fill_blank": [ModelType.T5_GENERATOR, ModelType.LIGHTWEIGHT],
            "improve_question": [ModelType.T5_GENERATOR],
            "paraphrase_question": [ModelType.T5_GENERATOR],
            "score_quality": [ModelType.BERT_VALIDATOR],
            "evaluate_distractors": [ModelType.BERT_VALIDATOR]
        }
        
        self.log_info(f"Initialized InferencePipeline for {tier.value} tier")
    
    def run_inference(self, request: InferenceRequest) -> InferenceResult:
        """
        Run inference for a single request.
        
        Args:
            request: Inference request
            
        Returns:
            Inference result
        """
        start_time = time.time()
        
        try:
            # Get appropriate models for task
            candidate_models = self._get_candidate_models(request.task, request.tier)
            
            if not candidate_models:
                return InferenceResult(
                    success=False,
                    output=None,
                    confidence=0.0,
                    processing_time=time.time() - start_time,
                    model_used="none",
                    tier=request.tier,
                    error=f"No models available for task: {request.task}"
                )
            
            # Try models in order of preference
            last_error = None
            for model_type in candidate_models:
                try:
                    result = self._run_task_with_model(request, model_type)
                    result.processing_time = time.time() - start_time
                    return result
                    
                except Exception as e:
                    last_error = str(e)
                    self.log_warning(f"Model {model_type.value} failed for task {request.task}: {e}")
                    continue
            
            # All models failed
            return InferenceResult(
                success=False,
                output=None,
                confidence=0.0,
                processing_time=time.time() - start_time,
                model_used="failed",
                tier=request.tier,
                error=f"All models failed. Last error: {last_error}"
            )
            
        except Exception as e:
            self.log_error(f"Inference pipeline failed: {str(e)}")
            return InferenceResult(
                success=False,
                output=None,
                confidence=0.0,
                processing_time=time.time() - start_time,
                model_used="error",
                tier=request.tier,
                error=str(e)
            )
    
    def run_batch_inference(self, requests: List[InferenceRequest]) -> List[InferenceResult]:
        """
        Run inference for multiple requests.
        
        Args:
            requests: List of inference requests
            
        Returns:
            List of inference results
        """
        self.log_info(f"Running batch inference for {len(requests)} requests")
        
        results = []
        for i, request in enumerate(requests):
            try:
                result = self.run_inference(request)
                results.append(result)
                
                if (i + 1) % 10 == 0:
                    self.log_info(f"Processed {i + 1}/{len(requests)} requests")
                    
            except Exception as e:
                self.log_error(f"Batch inference failed for request {i}: {str(e)}")
                results.append(InferenceResult(
                    success=False,
                    output=None,
                    confidence=0.0,
                    processing_time=0.0,
                    model_used="error",
                    tier=request.tier,
                    error=str(e)
                ))
        
        self.log_info(f"Completed batch inference: {len(results)} results")
        return results
    
    def _get_candidate_models(self, task: str, tier: ModelTier) -> List[ModelType]:
        """
        Get candidate models for a task based on tier.
        
        Args:
            task: Task name
            tier: License tier
            
        Returns:
            List of candidate model types in order of preference
        """
        # Get models for task
        task_models = self.task_models.get(task, [])
        
        # Filter by tier availability
        available_models = self.model_manager.get_available_models()
        
        candidates = [
            model_type for model_type in task_models
            if model_type in available_models
        ]
        
        # Add fallback models if enabled
        if self.enable_fallback and not candidates:
            if ModelType.LIGHTWEIGHT in available_models:
                candidates.append(ModelType.LIGHTWEIGHT)
        
        return candidates
    
    def _run_task_with_model(self, request: InferenceRequest, 
                           model_type: ModelType) -> InferenceResult:
        """
        Run a specific task with a specific model.
        
        Args:
            request: Inference request
            model_type: Model type to use
            
        Returns:
            Inference result
        """
        # Get model
        model = self.model_manager.get_model(model_type)
        
        # Route to appropriate method based on task
        if request.task == "enhance_question":
            return self._enhance_question(model, request)
        elif request.task == "validate_answer":
            return self._validate_answer(model, request)
        elif request.task == "generate_fill_blank":
            return self._generate_fill_blank(model, request)
        elif request.task == "improve_question":
            return self._improve_question(model, request)
        elif request.task == "score_quality":
            return self._score_quality(model, request)
        else:
            raise ProcessingError(
                stage="task_routing",
                message=f"Unknown task: {request.task}"
            )
    
    def _enhance_question(self, model, request: InferenceRequest) -> InferenceResult:
        """Enhance question using AI quality enhancer."""
        question = Question(**request.input_data["question"])
        context = request.input_data.get("context", "")
        
        if hasattr(model, 'enhance_question'):
            result = model.enhance_question(question, context)
            return InferenceResult(
                success=True,
                output=result,
                confidence=result.enhanced_question.confidence,
                processing_time=result.processing_time,
                model_used=model.__class__.__name__,
                tier=request.tier,
                metadata={"improvements": result.improvements}
            )
        else:
            raise ProcessingError(
                stage="model_method",
                message=f"Model {model.__class__.__name__} does not support enhance_question"
            )
    
    def _validate_answer(self, model, request: InferenceRequest) -> InferenceResult:
        """Validate answer using BERT validator."""
        question = request.input_data["question"]
        answer = request.input_data["answer"]
        context = request.input_data.get("context", "")
        
        if hasattr(model, 'validate_answer'):
            result = model.validate_answer(question, answer, context)
            return InferenceResult(
                success=True,
                output=result,
                confidence=result.confidence,
                processing_time=0.0,  # BERT doesn't track time separately
                model_used=model.__class__.__name__,
                tier=request.tier,
                metadata={"is_valid": result.is_valid}
            )
        else:
            raise ProcessingError(
                stage="model_method",
                message=f"Model {model.__class__.__name__} does not support validate_answer"
            )
    
    def _generate_fill_blank(self, model, request: InferenceRequest) -> InferenceResult:
        """Generate fill-blank question."""
        text = request.input_data["text"]
        
        if hasattr(model, 'generate_fill_blank_question'):
            result = model.generate_fill_blank_question(text)
            return InferenceResult(
                success=True,
                output=result,
                confidence=result.confidence,
                processing_time=result.processing_time,
                model_used=model.__class__.__name__,
                tier=request.tier
            )
        elif hasattr(model, 'predict'):
            # Fallback to basic predict method
            result = model.predict(text)
            return InferenceResult(
                success=True,
                output=result,
                confidence=result.confidence,
                processing_time=result.processing_time,
                model_used=model.__class__.__name__,
                tier=request.tier
            )
        else:
            raise ProcessingError(
                stage="model_method",
                message=f"Model {model.__class__.__name__} does not support fill-blank generation"
            )
    
    def _improve_question(self, model, request: InferenceRequest) -> InferenceResult:
        """Improve question using T5."""
        question = request.input_data["question"]
        
        if hasattr(model, 'improve_question'):
            result = model.improve_question(question)
            return InferenceResult(
                success=True,
                output=result,
                confidence=result.confidence,
                processing_time=result.processing_time,
                model_used=model.__class__.__name__,
                tier=request.tier
            )
        else:
            raise ProcessingError(
                stage="model_method",
                message=f"Model {model.__class__.__name__} does not support improve_question"
            )
    
    def _score_quality(self, model, request: InferenceRequest) -> InferenceResult:
        """Score question quality using BERT."""
        question = request.input_data["question"]
        context = request.input_data.get("context", "")
        
        if hasattr(model, 'score_question_quality'):
            result = model.score_question_quality(question, context)
            overall_quality = result.get("overall_quality", 0.5)
            return InferenceResult(
                success=True,
                output=result,
                confidence=overall_quality,
                processing_time=0.0,
                model_used=model.__class__.__name__,
                tier=request.tier,
                metadata=result
            )
        else:
            raise ProcessingError(
                stage="model_method",
                message=f"Model {model.__class__.__name__} does not support score_question_quality"
            )
    
    def get_pipeline_stats(self) -> Dict[str, Any]:
        """Get pipeline statistics."""
        return {
            "tier": self.tier.value,
            "enable_fallback": self.enable_fallback,
            "model_manager_stats": self.model_manager.get_model_info(),
            "supported_tasks": list(self.task_models.keys())
        }
    
    def cleanup(self) -> None:
        """Cleanup pipeline resources."""
        self.model_manager.unload_all_models()
        self.log_info("Inference pipeline cleaned up")

"""
URL Processor

This module handles processing of web URLs to extract text content.
"""

import re
import time
import asyncio
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse, urljoin
from urllib.robotparser import <PERSON><PERSON><PERSON>Parser
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from multiprocessing import cpu_count

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False

try:
    import aiohttp
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False

from .base import BaseInputProcessor


class URLProcessor(BaseInputProcessor):
    """Processor for web URLs."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize URLProcessor.
        
        Args:
            config: Configuration dictionary
        """
        super().__init__(config)
        
        # Check required libraries
        if not REQUESTS_AVAILABLE:
            raise ImportError("requests library is required for URL processing")
        
        if not BS4_AVAILABLE:
            raise ImportError("beautifulsoup4 library is required for URL processing")
        
        # Configuration
        self.timeout = self.config.get('timeout', 30)
        self.max_content_length = self.config.get('max_content_length', 10 * 1024 * 1024)  # 10MB
        self.user_agent = self.config.get('user_agent', 'QuizAIGen/1.0 (+https://github.com/quizaigen)')
        self.respect_robots = self.config.get('respect_robots', True)
        self.delay_between_requests = self.config.get('delay_between_requests', 1.0)
        
        # Session for connection reuse
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self.user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
        
        # Async processing settings
        self.max_workers = min(cpu_count(), 8)  # Limit concurrent requests
        self.enable_async = True

        self.log_info(f"Initialized URLProcessor with {self.max_workers} max workers")
    
    def process(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        Process a URL and extract text content.
        
        Args:
            url: URL to process
            **kwargs: Additional processing options
        
        Returns:
            Dictionary containing extracted text and metadata
        """
        if not self._validate_url(url):
            raise ValueError(f"Invalid URL: {url}")
        
        self.log_info(f"Processing URL: {url}")
        
        # Check robots.txt if enabled
        if self.respect_robots and not self._check_robots_txt(url):
            raise PermissionError(f"Robots.txt disallows access to: {url}")
        
        try:
            # Fetch the content
            response = self._fetch_url(url, **kwargs)
            
            if not response['success']:
                return response
            
            # Extract text from HTML
            text_data = self._extract_text_from_html(response['content'], url)
            
            # Combine results
            result = {
                'text': text_data['text'],
                'metadata': {
                    **response['metadata'],
                    **text_data['metadata']
                },
                'success': True
            }
            
            return result
        
        except Exception as e:
            self.log_error(f"Error processing URL {url}: {str(e)}")
            return {
                'text': '',
                'metadata': {'url': url, 'error': str(e)},
                'success': False,
                'error': str(e)
            }
    
    def _validate_url(self, url: str) -> bool:
        """
        Validate URL format and scheme.
        
        Args:
            url: URL to validate
        
        Returns:
            True if URL is valid
        """
        try:
            parsed = urlparse(url)
            return parsed.scheme in ['http', 'https'] and parsed.netloc
        except Exception:
            return False
    
    def _check_robots_txt(self, url: str) -> bool:
        """
        Check if URL is allowed by robots.txt.
        
        Args:
            url: URL to check
        
        Returns:
            True if access is allowed
        """
        try:
            parsed_url = urlparse(url)
            robots_url = f"{parsed_url.scheme}://{parsed_url.netloc}/robots.txt"
            
            rp = RobotFileParser()
            rp.set_url(robots_url)
            rp.read()
            
            return rp.can_fetch(self.user_agent, url)
        
        except Exception as e:
            self.log_warning(f"Could not check robots.txt for {url}: {str(e)}")
            return True  # Allow if robots.txt check fails
    
    def _fetch_url(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        Fetch content from URL.
        
        Args:
            url: URL to fetch
            **kwargs: Additional options
        
        Returns:
            Dictionary with content and metadata
        """
        metadata = {
            'url': url,
            'fetch_time': time.time(),
            'method': 'GET'
        }
        
        try:
            # Add delay between requests
            time.sleep(self.delay_between_requests)
            
            # Make request
            response = self.session.get(
                url,
                timeout=self.timeout,
                stream=True,
                allow_redirects=True
            )
            
            # Check content length
            content_length = response.headers.get('content-length')
            if content_length and int(content_length) > self.max_content_length:
                raise ValueError(f"Content too large: {content_length} bytes")
            
            # Check content type
            content_type = response.headers.get('content-type', '').lower()
            if 'text/html' not in content_type and 'application/xhtml' not in content_type:
                self.log_warning(f"Unexpected content type: {content_type}")
            
            # Read content with size limit
            content = b''
            for chunk in response.iter_content(chunk_size=8192):
                content += chunk
                if len(content) > self.max_content_length:
                    raise ValueError(f"Content too large: {len(content)} bytes")
            
            # Update metadata
            metadata.update({
                'status_code': response.status_code,
                'content_type': content_type,
                'content_length': len(content),
                'encoding': response.encoding or 'utf-8',
                'final_url': response.url,
                'redirects': len(response.history)
            })
            
            # Check status code
            response.raise_for_status()
            
            # Decode content
            try:
                text_content = content.decode(response.encoding or 'utf-8')
            except UnicodeDecodeError:
                # Try common encodings
                for encoding in ['utf-8', 'latin-1', 'cp1252']:
                    try:
                        text_content = content.decode(encoding)
                        metadata['encoding'] = encoding
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    raise UnicodeDecodeError("Could not decode content")
            
            return {
                'content': text_content,
                'metadata': metadata,
                'success': True
            }
        
        except Exception as e:
            self.log_error(f"Error fetching URL {url}: {str(e)}")
            return {
                'content': '',
                'metadata': metadata,
                'success': False,
                'error': str(e)
            }
    
    def _extract_text_from_html(self, html_content: str, url: str) -> Dict[str, Any]:
        """
        Extract text content from HTML.
        
        Args:
            html_content: HTML content
            url: Original URL for context
        
        Returns:
            Dictionary with extracted text and metadata
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style", "nav", "footer", "header", "aside"]):
                script.decompose()
            
            # Extract title
            title = soup.find('title')
            title_text = title.get_text().strip() if title else ''
            
            # Extract main content
            # Try to find main content areas
            main_content = None
            for selector in ['main', 'article', '[role="main"]', '.content', '#content']:
                main_content = soup.select_one(selector)
                if main_content:
                    break
            
            if not main_content:
                # Fall back to body
                main_content = soup.find('body') or soup
            
            # Extract text from paragraphs and headings
            text_elements = main_content.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li', 'div'])
            
            text_parts = []
            for element in text_elements:
                text = element.get_text().strip()
                if text and len(text) > 10:  # Filter out very short text
                    text_parts.append(text)
            
            # Combine text
            full_text = '\n\n'.join(text_parts)
            
            # Clean text
            cleaned_text = self._clean_extracted_text(full_text)
            
            metadata = {
                'title': title_text,
                'text_elements_found': len(text_parts),
                'character_count': len(cleaned_text),
                'word_count': len(cleaned_text.split()),
                'extraction_method': 'BeautifulSoup'
            }
            
            return {
                'text': cleaned_text,
                'metadata': metadata
            }
        
        except Exception as e:
            self.log_error(f"Error extracting text from HTML: {str(e)}")
            return {
                'text': '',
                'metadata': {'error': str(e)}
            }
    
    def _clean_extracted_text(self, text: str) -> str:
        """
        Clean extracted text from web pages.
        
        Args:
            text: Raw extracted text
        
        Returns:
            Cleaned text
        """
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r' +', ' ', text)
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
        
        # Remove common web artifacts
        text = re.sub(r'(Cookie|Privacy) Policy.*?(?=\n\n|\Z)', '', text, flags=re.IGNORECASE | re.DOTALL)
        text = re.sub(r'Terms of Service.*?(?=\n\n|\Z)', '', text, flags=re.IGNORECASE | re.DOTALL)
        
        # Clean up lines
        lines = [line.strip() for line in text.split('\n')]
        lines = [line for line in lines if line]  # Remove empty lines
        
        text = '\n\n'.join(lines)
        text = text.strip()
        
        return text
    
    def process_url(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        Convenience method to process a single URL.
        
        Args:
            url: URL to process
            **kwargs: Additional options
        
        Returns:
            Processing result
        """
        return self.process(url, **kwargs)
    
    def process_multiple_urls(self, urls: List[str],
                            parallel: bool = True,
                            max_workers: Optional[int] = None,
                            **kwargs) -> List[Dict[str, Any]]:
        """
        Process multiple URLs with parallel processing support.

        Args:
            urls: List of URLs to process
            parallel: Enable parallel processing
            max_workers: Maximum number of workers (default: instance setting)
            **kwargs: Additional options

        Returns:
            List of processing results
        """
        if not urls:
            return []

        start_time = time.time()

        # Use instance max_workers if not specified
        if max_workers is None:
            max_workers = self.max_workers

        # Limit workers to number of URLs
        max_workers = min(max_workers, len(urls))

        self.log_info(f"Processing {len(urls)} URLs with parallel={parallel}, max_workers={max_workers}")

        if not parallel or not self.enable_async or len(urls) == 1:
            # Sequential processing
            results = self._process_urls_sequential(urls, **kwargs)
        else:
            # Parallel processing
            results = self._process_urls_parallel(urls, max_workers, **kwargs)

        # Add batch performance metrics
        processing_time = time.time() - start_time
        self._add_batch_metrics(results, processing_time, max_workers, parallel)

        self.log_info(f"URL processing completed: {len(results)} URLs in {processing_time:.2f}s")
        return results

    def _process_urls_sequential(self, urls: List[str], **kwargs) -> List[Dict[str, Any]]:
        """Process URLs sequentially."""
        results = []

        for i, url in enumerate(urls):
            try:
                self.log_info(f"Sequential processing ({i+1}/{len(urls)}): {url}")
                result = self.process(url, **kwargs)
                results.append(result)

                # Add delay between requests
                if i < len(urls) - 1:
                    time.sleep(self.delay_between_requests)

            except Exception as e:
                self.log_error(f"Sequential processing failed for {url}: {str(e)}")
                results.append(self._create_error_result(url, str(e)))

        return results

    def _process_urls_parallel(self, urls: List[str], max_workers: int, **kwargs) -> List[Dict[str, Any]]:
        """Process URLs using parallel processing."""
        results = [None] * len(urls)

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_index = {
                executor.submit(self._process_url_safe, url, **kwargs): i
                for i, url in enumerate(urls)
            }

            # Collect results
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    result = future.result()
                    results[index] = result
                    self.log_info(f"Parallel processing completed for URL {index + 1}/{len(urls)}")
                except Exception as e:
                    self.log_error(f"Parallel processing failed for URL {index}: {str(e)}")
                    results[index] = self._create_error_result(urls[index], str(e))

        return results

    def _process_url_safe(self, url: str, **kwargs) -> Dict[str, Any]:
        """Safely process a URL with error handling."""
        try:
            # Add staggered delay for parallel requests to be respectful
            time.sleep(self.delay_between_requests * 0.1)  # Reduced delay for parallel
            return self.process(url, **kwargs)
        except Exception as e:
            self.log_error(f"Safe URL processing failed for {url}: {str(e)}")
            return self._create_error_result(url, str(e))

    def _create_error_result(self, url: str, error_message: str) -> Dict[str, Any]:
        """Create a standardized error result."""
        return {
            'text': '',
            'metadata': {'url': url, 'error': error_message},
            'success': False,
            'error': error_message
        }

    def _add_batch_metrics(self, results: List[Dict[str, Any]],
                         processing_time: float, max_workers: int, parallel: bool):
        """Add batch performance metrics to results."""
        successful_results = [r for r in results if r.get('success', False)]
        failed_results = [r for r in results if not r.get('success', False)]

        total_urls = len(results)
        success_rate = len(successful_results) / total_urls if total_urls > 0 else 0

        # Calculate total text extracted
        total_characters = sum(len(r.get('text', '')) for r in successful_results)
        total_words = sum(len(r.get('text', '').split()) for r in successful_results)

        batch_metrics = {
            'batch_processing_time': processing_time,
            'parallel_processing': parallel,
            'max_workers_used': max_workers,
            'total_urls': total_urls,
            'successful_urls': len(successful_results),
            'failed_urls': len(failed_results),
            'success_rate': success_rate,
            'total_characters_extracted': total_characters,
            'total_words_extracted': total_words,
            'throughput_urls_per_second': total_urls / processing_time if processing_time > 0 else 0,
            'average_processing_time_per_url': processing_time / total_urls if total_urls > 0 else 0
        }

        # Add metrics to each result
        for result in results:
            result['batch_metrics'] = batch_metrics

    async def process_multiple_urls_async(self, urls: List[str],
                                        max_concurrent: Optional[int] = None,
                                        **kwargs) -> List[Dict[str, Any]]:
        """
        Process multiple URLs asynchronously using aiohttp.

        Args:
            urls: List of URLs to process
            max_concurrent: Maximum concurrent requests (default: instance setting)
            **kwargs: Additional options

        Returns:
            List of processing results
        """
        if not AIOHTTP_AVAILABLE:
            self.log_warning("aiohttp not available, falling back to parallel processing")
            return self.process_multiple_urls(urls, parallel=True, **kwargs)

        if not urls:
            return []

        start_time = time.time()

        # Use instance max_workers if not specified
        if max_concurrent is None:
            max_concurrent = self.max_workers

        # Limit concurrent requests
        max_concurrent = min(max_concurrent, len(urls))

        self.log_info(f"Processing {len(urls)} URLs asynchronously with {max_concurrent} max concurrent")

        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)

        # Process URLs asynchronously
        tasks = [self._process_url_async(url, semaphore, **kwargs) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Handle exceptions in results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.log_error(f"Async processing failed for URL {i}: {str(result)}")
                processed_results.append(self._create_error_result(urls[i], str(result)))
            else:
                processed_results.append(result)

        # Add batch performance metrics
        processing_time = time.time() - start_time
        self._add_batch_metrics(processed_results, processing_time, max_concurrent, True)

        self.log_info(f"Async URL processing completed: {len(processed_results)} URLs in {processing_time:.2f}s")
        return processed_results

    async def _process_url_async(self, url: str, semaphore: asyncio.Semaphore, **kwargs) -> Dict[str, Any]:
        """Process a single URL asynchronously."""
        async with semaphore:
            try:
                # Add staggered delay
                await asyncio.sleep(self.delay_between_requests * 0.05)  # Very small delay for async

                # Validate URL
                if not self._validate_url(url):
                    raise ValueError(f"Invalid URL: {url}")

                # Check robots.txt if enabled (sync operation)
                if self.respect_robots and not self._check_robots_txt(url):
                    raise PermissionError(f"Robots.txt disallows access to: {url}")

                # Fetch content asynchronously
                async with aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=self.timeout),
                    headers={'User-Agent': self.user_agent}
                ) as session:
                    async with session.get(url) as response:
                        # Check content length
                        content_length = response.headers.get('content-length')
                        if content_length and int(content_length) > self.max_content_length:
                            raise ValueError(f"Content too large: {content_length} bytes")

                        # Check content type
                        content_type = response.headers.get('content-type', '').lower()
                        if 'text/html' not in content_type and 'application/xhtml' not in content_type:
                            self.log_warning(f"Unexpected content type: {content_type}")

                        # Read content
                        content = await response.text()

                        # Extract text from HTML
                        text_data = self._extract_text_from_html(content, url)

                        # Create result
                        result = {
                            'text': text_data['text'],
                            'metadata': {
                                'url': url,
                                'fetch_time': time.time(),
                                'method': 'GET',
                                'status_code': response.status,
                                'content_type': content_type,
                                'async_processing': True,
                                **text_data['metadata']
                            },
                            'success': True
                        }

                        return result

            except Exception as e:
                self.log_error(f"Async processing failed for {url}: {str(e)}")
                return self._create_error_result(url, str(e))

    def run_async_processing(self, urls: List[str], **kwargs) -> List[Dict[str, Any]]:
        """
        Convenience method to run async processing in a sync context.

        Args:
            urls: List of URLs to process
            **kwargs: Additional options

        Returns:
            List of processing results
        """
        try:
            # Try to get existing event loop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If loop is already running, create a new task
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self.process_multiple_urls_async(urls, **kwargs))
                    return future.result()
            else:
                # Run in existing loop
                return loop.run_until_complete(self.process_multiple_urls_async(urls, **kwargs))
        except RuntimeError:
            # No event loop, create new one
            return asyncio.run(self.process_multiple_urls_async(urls, **kwargs))

"""
Enhanced Document Processor

This module handles processing of various document formats including PDF and Word files
with advanced features like OCR, metadata extraction, and content analysis.
"""

import os
import re
import logging
from typing import Dict, Any, Optional, List, Union, Tuple
from pathlib import Path
from datetime import datetime
import hashlib

# Core dependencies
try:
    import PyPDF2
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

# Advanced PDF processing
try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False

# OCR capabilities
try:
    import pytesseract
    from PIL import Image
    import pdf2image
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False

# Text processing
try:
    from textstat import flesch_reading_ease, flesch_kincaid_grade
    TEXTSTAT_AVAILABLE = True
except ImportError:
    TEXTSTAT_AVAILABLE = False

from .base import BaseInputProcessor
from ..core.exceptions import ProcessingError, ValidationError
from ..utils.text_utils import clean_text, extract_sentences, tokenize_text


class DocumentProcessor(BaseInputProcessor):
    """Enhanced processor for document files with advanced features."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize Enhanced DocumentProcessor.

        Args:
            config: Configuration dictionary
        """
        super().__init__(config)

        # Supported file extensions
        self.supported_extensions = {'.pdf', '.docx', '.doc'}

        # Processing capabilities
        self.capabilities = {
            'basic_pdf': PDF_AVAILABLE,
            'advanced_pdf': PDFPLUMBER_AVAILABLE,
            'word_processing': DOCX_AVAILABLE,
            'ocr_processing': OCR_AVAILABLE,
            'readability_analysis': TEXTSTAT_AVAILABLE
        }

        # Check available libraries and adjust capabilities
        if not PDF_AVAILABLE:
            self.log_warning("PyPDF2 not available - basic PDF processing disabled")
            self.supported_extensions.discard('.pdf')

        if not DOCX_AVAILABLE:
            self.log_warning("python-docx not available - Word processing disabled")
            self.supported_extensions.discard('.docx')
            self.supported_extensions.discard('.doc')

        # Advanced features availability
        if PDFPLUMBER_AVAILABLE:
            self.log_info("pdfplumber available - enhanced PDF processing enabled")

        if OCR_AVAILABLE:
            self.log_info("OCR capabilities available - image text extraction enabled")

        if TEXTSTAT_AVAILABLE:
            self.log_info("Readability analysis available")

        # Configuration options
        self.max_file_size = self.config.get('max_file_size_mb', 50) * 1024 * 1024
        self.enable_ocr = self.config.get('enable_ocr', True) and OCR_AVAILABLE
        self.ocr_language = self.config.get('ocr_language', 'eng')
        self.extract_metadata = self.config.get('extract_metadata', True)
        self.analyze_readability = self.config.get('analyze_readability', True) and TEXTSTAT_AVAILABLE

        self.log_info(f"Enhanced DocumentProcessor initialized with capabilities: {self.capabilities}")
        self.log_info(f"Supported formats: {self.supported_extensions}")
    
    def process(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """
        Process a document file with advanced extraction and analysis.

        Args:
            file_path: Path to the document file
            **kwargs: Additional processing options:
                - use_ocr: Enable OCR for image-based text (default: config setting)
                - extract_tables: Extract table data (default: True)
                - analyze_structure: Analyze document structure (default: True)
                - quality_threshold: Minimum text quality threshold (default: 0.5)

        Returns:
            Dictionary containing extracted text, metadata, and analysis
        """
        file_path = Path(file_path)

        # Validate file
        if not self.validate_file(str(file_path)):
            raise ValidationError(f"File validation failed: {file_path}")

        extension = file_path.suffix.lower()

        self.log_info(f"Processing document: {file_path} ({extension})")

        # Processing options
        use_ocr = kwargs.get('use_ocr', self.enable_ocr)
        extract_tables = kwargs.get('extract_tables', True)
        analyze_structure = kwargs.get('analyze_structure', True)
        quality_threshold = kwargs.get('quality_threshold', 0.5)

        try:
            # Process based on file type
            if extension == '.pdf':
                result = self._process_pdf_enhanced(file_path, use_ocr=use_ocr,
                                                  extract_tables=extract_tables, **kwargs)
            elif extension in ['.docx', '.doc']:
                result = self._process_word_enhanced(file_path, extract_tables=extract_tables,
                                                   analyze_structure=analyze_structure, **kwargs)
            else:
                raise ValueError(f"Unsupported file format: {extension}")

            # Post-processing enhancements
            if result.get('success', False):
                result = self._enhance_processing_result(result, quality_threshold)

            return result

        except Exception as e:
            self.log_error(f"Error processing document {file_path}: {str(e)}")
            raise ProcessingError(
                stage="document_processing",
                message=f"Failed to process document {file_path}: {str(e)}"
            )
    
    def _process_pdf_enhanced(self, file_path: Path, use_ocr: bool = False,
                            extract_tables: bool = True, **kwargs) -> Dict[str, Any]:
        """
        Enhanced PDF processing with multiple extraction methods.

        Args:
            file_path: Path to PDF file
            use_ocr: Enable OCR for image-based text
            extract_tables: Extract table data
            **kwargs: Additional options

        Returns:
            Dictionary with extracted text, tables, and comprehensive metadata
        """
        if not PDF_AVAILABLE:
            raise ImportError("PyPDF2 is required for PDF processing")

        metadata = self._initialize_pdf_metadata(file_path)
        text_content = []
        tables_data = []
        extraction_methods = []

        try:
            # Method 1: Try pdfplumber first (better for complex layouts)
            if PDFPLUMBER_AVAILABLE:
                result = self._extract_with_pdfplumber(file_path, extract_tables)
                if result['text']:
                    text_content.extend(result['text'])
                    tables_data.extend(result.get('tables', []))
                    extraction_methods.append('pdfplumber')
                    metadata.update(result['metadata'])

            # Method 2: Fallback to PyPDF2 if pdfplumber fails or unavailable
            if not text_content:
                result = self._extract_with_pypdf2(file_path)
                if result['text']:
                    text_content.extend(result['text'])
                    extraction_methods.append('PyPDF2')
                    metadata.update(result['metadata'])

            # Method 3: OCR as last resort for image-based PDFs
            if use_ocr and not text_content and OCR_AVAILABLE:
                result = self._extract_with_ocr(file_path)
                if result['text']:
                    text_content.extend(result['text'])
                    extraction_methods.append('OCR')
                    metadata.update(result['metadata'])

            # Combine and process extracted text
            full_text = '\n\n'.join(text_content) if text_content else ''
            cleaned_text = self._clean_extracted_text(full_text)

            # Update metadata
            metadata.update({
                'extraction_methods': extraction_methods,
                'character_count': len(cleaned_text),
                'word_count': len(cleaned_text.split()) if cleaned_text else 0,
                'tables_found': len(tables_data),
                'extraction_success': bool(cleaned_text.strip())
            })

            if not cleaned_text.strip():
                self.log_warning(f"No text content extracted from PDF: {file_path}")

            return {
                'text': cleaned_text,
                'tables': tables_data,
                'metadata': metadata,
                'success': bool(cleaned_text.strip())
            }

        except Exception as e:
            self.log_error(f"Error in enhanced PDF processing: {str(e)}")
            return {
                'text': '',
                'tables': [],
                'metadata': metadata,
                'success': False,
                'error': str(e)
            }

    def _initialize_pdf_metadata(self, file_path: Path) -> Dict[str, Any]:
        """Initialize PDF metadata structure."""
        return {
            'file_path': str(file_path),
            'file_type': 'pdf',
            'file_size': file_path.stat().st_size,
            'processing_timestamp': datetime.now().isoformat(),
            'file_hash': self._calculate_file_hash(file_path),
            'pages': 0,
            'extraction_methods': [],
            'processing_time_seconds': 0
        }

    def _extract_with_pdfplumber(self, file_path: Path, extract_tables: bool) -> Dict[str, Any]:
        """Extract text and tables using pdfplumber."""
        if not PDFPLUMBER_AVAILABLE:
            return {'text': [], 'tables': [], 'metadata': {}}

        import pdfplumber
        text_content = []
        tables_data = []

        try:
            with pdfplumber.open(file_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    # Extract text
                    page_text = page.extract_text()
                    if page_text and page_text.strip():
                        text_content.append(page_text)

                    # Extract tables if requested
                    if extract_tables:
                        tables = page.extract_tables()
                        for table in tables:
                            if table:
                                tables_data.append({
                                    'page': page_num + 1,
                                    'data': table,
                                    'rows': len(table),
                                    'columns': len(table[0]) if table else 0
                                })

                return {
                    'text': text_content,
                    'tables': tables_data,
                    'metadata': {
                        'pages': len(pdf.pages),
                        'pdfplumber_version': pdfplumber.__version__ if hasattr(pdfplumber, '__version__') else 'unknown'
                    }
                }
        except Exception as e:
            self.log_warning(f"pdfplumber extraction failed: {str(e)}")
            return {'text': [], 'tables': [], 'metadata': {}}

    def _extract_with_pypdf2(self, file_path: Path) -> Dict[str, Any]:
        """Extract text using PyPDF2 as fallback."""
        if not PDF_AVAILABLE:
            return {'text': [], 'metadata': {}}

        import PyPDF2
        text_content = []

        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)

                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text and page_text.strip():
                            text_content.append(page_text)
                    except Exception as e:
                        self.log_warning(f"Error extracting page {page_num + 1} with PyPDF2: {str(e)}")
                        continue

                return {
                    'text': text_content,
                    'metadata': {
                        'pages': len(pdf_reader.pages),
                        'pypdf2_version': PyPDF2.__version__ if hasattr(PyPDF2, '__version__') else 'unknown'
                    }
                }
        except Exception as e:
            self.log_warning(f"PyPDF2 extraction failed: {str(e)}")
            return {'text': [], 'metadata': {}}

    def _extract_with_ocr(self, file_path: Path) -> Dict[str, Any]:
        """Extract text using OCR for image-based PDFs."""
        if not OCR_AVAILABLE:
            return {'text': [], 'metadata': {}}

        import pdf2image
        import pytesseract
        from PIL import Image

        text_content = []

        try:
            # Convert PDF pages to images
            images = pdf2image.convert_from_path(file_path)

            for page_num, image in enumerate(images):
                try:
                    # Perform OCR on the image
                    page_text = pytesseract.image_to_string(image, lang=self.ocr_language)
                    if page_text and page_text.strip():
                        text_content.append(page_text)
                except Exception as e:
                    self.log_warning(f"OCR failed for page {page_num + 1}: {str(e)}")
                    continue

            return {
                'text': text_content,
                'metadata': {
                    'pages': len(images),
                    'ocr_language': self.ocr_language,
                    'ocr_method': 'pytesseract'
                }
            }
        except Exception as e:
            self.log_warning(f"OCR extraction failed: {str(e)}")
            return {'text': [], 'metadata': {}}

    def _process_word_enhanced(self, file_path: Path, extract_tables: bool = True,
                             analyze_structure: bool = True, **kwargs) -> Dict[str, Any]:
        """
        Enhanced Word document processing with structure analysis.

        Args:
            file_path: Path to Word document
            extract_tables: Extract table data
            analyze_structure: Analyze document structure
            **kwargs: Additional options

        Returns:
            Dictionary with extracted text, tables, and structure analysis
        """
        if not DOCX_AVAILABLE:
            raise ImportError("python-docx is required for Word document processing")

        from docx import Document

        metadata = {
            'file_path': str(file_path),
            'file_type': 'word',
            'file_size': file_path.stat().st_size,
            'processing_timestamp': datetime.now().isoformat(),
            'file_hash': self._calculate_file_hash(file_path),
            'extraction_method': 'python-docx'
        }

        try:
            # Load the document
            doc = Document(file_path)

            # Extract text content
            text_content = []
            structure_info = {
                'headings': [],
                'paragraphs': 0,
                'lists': 0,
                'images': 0,
                'hyperlinks': 0
            }

            # Process paragraphs and analyze structure
            for para in doc.paragraphs:
                if para.text.strip():
                    text_content.append(para.text)
                    structure_info['paragraphs'] += 1

                    # Analyze paragraph style for structure
                    if analyze_structure and para.style.name.startswith('Heading'):
                        structure_info['headings'].append({
                            'level': para.style.name,
                            'text': para.text.strip(),
                            'position': len(text_content) - 1
                        })

            # Extract tables if requested
            tables_data = []
            if extract_tables and doc.tables:
                for table_idx, table in enumerate(doc.tables):
                    table_data = []
                    for row in table.rows:
                        row_data = []
                        for cell in row.cells:
                            row_data.append(cell.text.strip())
                        table_data.append(row_data)

                    if table_data:
                        tables_data.append({
                            'table_index': table_idx,
                            'data': table_data,
                            'rows': len(table_data),
                            'columns': len(table_data[0]) if table_data else 0
                        })

                        # Add table content to text
                        table_text = []
                        for row in table_data:
                            table_text.append(' | '.join(row))
                        text_content.append('\n'.join(table_text))

            # Combine all text
            full_text = '\n\n'.join(text_content)
            cleaned_text = self._clean_extracted_text(full_text)

            # Update metadata
            metadata.update({
                'paragraphs': structure_info['paragraphs'],
                'headings_count': len(structure_info['headings']),
                'tables_count': len(tables_data),
                'character_count': len(cleaned_text),
                'word_count': len(cleaned_text.split()) if cleaned_text else 0,
                'structure_analysis': structure_info if analyze_structure else None
            })

            if not cleaned_text.strip():
                self.log_warning(f"No text content extracted from Word document: {file_path}")

            return {
                'text': cleaned_text,
                'tables': tables_data,
                'structure': structure_info if analyze_structure else None,
                'metadata': metadata,
                'success': bool(cleaned_text.strip())
            }

        except Exception as e:
            self.log_error(f"Error in enhanced Word processing: {str(e)}")
            return {
                'text': '',
                'tables': [],
                'structure': None,
                'metadata': metadata,
                'success': False,
                'error': str(e)
            }

    def _enhance_processing_result(self, result: Dict[str, Any], quality_threshold: float) -> Dict[str, Any]:
        """
        Enhance processing result with additional analysis.

        Args:
            result: Processing result dictionary
            quality_threshold: Minimum quality threshold

        Returns:
            Enhanced result dictionary
        """
        if not result.get('success', False) or not result.get('text'):
            return result

        text = result['text']

        # Text quality analysis
        quality_metrics = self._analyze_text_quality(text)
        result['quality_metrics'] = quality_metrics

        # Readability analysis
        if self.analyze_readability and TEXTSTAT_AVAILABLE:
            readability_metrics = self._analyze_readability(text)
            result['readability_metrics'] = readability_metrics

        # Content structure analysis
        content_analysis = self._analyze_content_structure(text)
        result['content_analysis'] = content_analysis

        # Quality check
        overall_quality = quality_metrics.get('overall_score', 0.0)
        result['quality_passed'] = overall_quality >= quality_threshold

        if not result['quality_passed']:
            self.log_warning(f"Document quality below threshold: {overall_quality:.2f} < {quality_threshold}")

        return result

    def _analyze_text_quality(self, text: str) -> Dict[str, Any]:
        """Analyze text quality metrics."""
        if not text:
            return {'overall_score': 0.0, 'issues': ['empty_text']}

        issues = []
        metrics = {}

        # Basic metrics
        char_count = len(text)
        word_count = len(text.split())
        sentence_count = len(extract_sentences(text))

        metrics.update({
            'character_count': char_count,
            'word_count': word_count,
            'sentence_count': sentence_count,
            'avg_word_length': sum(len(word) for word in text.split()) / word_count if word_count > 0 else 0,
            'avg_sentence_length': word_count / sentence_count if sentence_count > 0 else 0
        })

        # Quality checks
        if word_count < 10:
            issues.append('too_short')

        if char_count > 0:
            # Check for excessive special characters
            special_char_ratio = sum(1 for c in text if not c.isalnum() and not c.isspace()) / char_count
            if special_char_ratio > 0.3:
                issues.append('excessive_special_chars')
            metrics['special_char_ratio'] = special_char_ratio

            # Check for repeated characters (OCR artifacts)
            repeated_chars = len(re.findall(r'(.)\1{3,}', text))
            if repeated_chars > 5:
                issues.append('repeated_characters')
            metrics['repeated_char_sequences'] = repeated_chars

        # Calculate overall quality score
        base_score = 1.0
        if 'too_short' in issues:
            base_score -= 0.3
        if 'excessive_special_chars' in issues:
            base_score -= 0.2
        if 'repeated_characters' in issues:
            base_score -= 0.2

        metrics.update({
            'overall_score': max(0.0, base_score),
            'issues': issues
        })

        return metrics

    def _analyze_readability(self, text: str) -> Dict[str, Any]:
        """Analyze text readability using textstat."""
        if not TEXTSTAT_AVAILABLE or not text:
            return {}

        try:
            from textstat import flesch_reading_ease, flesch_kincaid_grade, automated_readability_index

            return {
                'flesch_reading_ease': flesch_reading_ease(text),
                'flesch_kincaid_grade': flesch_kincaid_grade(text),
                'automated_readability_index': automated_readability_index(text),
                'reading_level': self._determine_reading_level(flesch_reading_ease(text))
            }
        except Exception as e:
            self.log_warning(f"Readability analysis failed: {str(e)}")
            return {}

    def _determine_reading_level(self, flesch_score: float) -> str:
        """Determine reading level from Flesch score."""
        if flesch_score >= 90:
            return 'very_easy'
        elif flesch_score >= 80:
            return 'easy'
        elif flesch_score >= 70:
            return 'fairly_easy'
        elif flesch_score >= 60:
            return 'standard'
        elif flesch_score >= 50:
            return 'fairly_difficult'
        elif flesch_score >= 30:
            return 'difficult'
        else:
            return 'very_difficult'

    def _analyze_content_structure(self, text: str) -> Dict[str, Any]:
        """Analyze content structure and organization."""
        sentences = extract_sentences(text)
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]

        return {
            'sentence_count': len(sentences),
            'paragraph_count': len(paragraphs),
            'avg_sentences_per_paragraph': len(sentences) / len(paragraphs) if paragraphs else 0,
            'short_sentences': sum(1 for s in sentences if len(s.split()) < 10),
            'long_sentences': sum(1 for s in sentences if len(s.split()) > 25),
            'question_sentences': sum(1 for s in sentences if s.strip().endswith('?')),
            'exclamation_sentences': sum(1 for s in sentences if s.strip().endswith('!'))
        }

    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA-256 hash of file for integrity checking."""
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            self.log_warning(f"Failed to calculate file hash: {str(e)}")
            return "unknown"
    
    def _clean_extracted_text(self, text: str) -> str:
        """
        Clean extracted text from documents.
        
        Args:
            text: Raw extracted text
        
        Returns:
            Cleaned text
        """
        if not text:
            return ""
        
        # Remove excessive whitespace
        import re
        
        # Replace multiple spaces with single space
        text = re.sub(r' +', ' ', text)
        
        # Replace multiple newlines with double newline
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
        
        # Remove leading/trailing whitespace from each line
        lines = [line.strip() for line in text.split('\n')]
        text = '\n'.join(lines)
        
        # Remove empty lines at start and end
        text = text.strip()
        
        return text
    
    def validate_file(self, file_path: str) -> bool:
        """
        Enhanced file validation with comprehensive checks.

        Args:
            file_path: Path to the file

        Returns:
            True if file can be processed
        """
        try:
            file_path = Path(file_path)

            # Basic existence and type checks
            if not file_path.exists():
                self.log_warning(f"File does not exist: {file_path}")
                return False

            if not file_path.is_file():
                self.log_warning(f"Path is not a file: {file_path}")
                return False

            # Extension check
            extension = file_path.suffix.lower()
            if extension not in self.supported_extensions:
                self.log_warning(f"Unsupported file extension: {extension}")
                return False

            # File size check
            file_size = file_path.stat().st_size
            if file_size > self.max_file_size:
                self.log_warning(f"File too large: {file_size} bytes (max: {self.max_file_size})")
                return False

            if file_size == 0:
                self.log_warning(f"File is empty: {file_path}")
                return False

            # File permissions check
            if not os.access(file_path, os.R_OK):
                self.log_warning(f"File is not readable: {file_path}")
                return False

            return True

        except Exception as e:
            self.log_error(f"Error validating file {file_path}: {str(e)}")
            return False
    
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported file formats.
        
        Returns:
            List of supported file extensions
        """
        return list(self.supported_extensions)
    
    def process_pdf(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """
        Convenience method to process PDF files.
        
        Args:
            file_path: Path to PDF file
            **kwargs: Additional options
        
        Returns:
            Processing result
        """
        return self.process(file_path, **kwargs)
    
    def process_word(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """
        Convenience method to process Word documents.

        Args:
            file_path: Path to Word document
            **kwargs: Additional options

        Returns:
            Processing result
        """
        return self.process(file_path, **kwargs)

    def batch_process(self, file_paths: List[str], **kwargs) -> List[Dict[str, Any]]:
        """
        Process multiple documents in batch.

        Args:
            file_paths: List of file paths to process
            **kwargs: Additional processing options

        Returns:
            List of processing results
        """
        results = []

        for file_path in file_paths:
            try:
                self.log_info(f"Batch processing: {file_path}")
                result = self.process(file_path, **kwargs)
                results.append(result)
            except Exception as e:
                self.log_error(f"Batch processing failed for {file_path}: {str(e)}")
                results.append({
                    'file_path': file_path,
                    'success': False,
                    'error': str(e),
                    'text': '',
                    'metadata': {}
                })

        return results

    def get_processing_capabilities(self) -> Dict[str, Any]:
        """
        Get detailed information about processing capabilities.

        Returns:
            Dictionary with capability information
        """
        return {
            'supported_formats': list(self.supported_extensions),
            'capabilities': self.capabilities,
            'max_file_size_mb': self.max_file_size / (1024 * 1024),
            'ocr_enabled': self.enable_ocr,
            'ocr_language': self.ocr_language if self.enable_ocr else None,
            'readability_analysis': self.analyze_readability,
            'metadata_extraction': self.extract_metadata,
            'advanced_features': {
                'table_extraction': True,
                'structure_analysis': True,
                'quality_assessment': True,
                'batch_processing': True,
                'file_integrity_checking': True
            }
        }

    def extract_text_only(self, file_path: str, **kwargs) -> str:
        """
        Extract only text content from document (simplified interface).

        Args:
            file_path: Path to document
            **kwargs: Additional options

        Returns:
            Extracted text content
        """
        result = self.process(file_path, **kwargs)
        return result.get('text', '') if result.get('success', False) else ''

    def get_document_info(self, file_path: str) -> Dict[str, Any]:
        """
        Get basic document information without full processing.

        Args:
            file_path: Path to document

        Returns:
            Basic document information
        """
        file_path = Path(file_path)

        if not file_path.exists():
            return {'error': 'File not found'}

        info = {
            'file_path': str(file_path),
            'file_name': file_path.name,
            'file_size': file_path.stat().st_size,
            'file_extension': file_path.suffix.lower(),
            'is_supported': file_path.suffix.lower() in self.supported_extensions,
            'can_process': self.validate_file(str(file_path)),
            'file_hash': self._calculate_file_hash(file_path)
        }

        # Add format-specific quick info
        extension = file_path.suffix.lower()
        if extension == '.pdf' and PDF_AVAILABLE:
            try:
                import PyPDF2
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    info['page_count'] = len(pdf_reader.pages)
            except Exception:
                pass

        elif extension in ['.docx', '.doc'] and DOCX_AVAILABLE:
            try:
                from docx import Document
                doc = Document(file_path)
                info['paragraph_count'] = len([p for p in doc.paragraphs if p.text.strip()])
                info['table_count'] = len(doc.tables)
            except Exception:
                pass

        return info

"""
Logging utilities for QuizAIGen

Provides consistent logging across the library with configurable levels and formats.
"""

import logging
import sys
from typing import Optional, Dict, Any
from pathlib import Path


class LoggerMixin:
    """
    Mixin class to add logging capabilities to any class.
    
    Provides consistent logging methods across the library.
    """
    
    def __init__(self, *args, **kwargs):
        """Initialize logger mixin."""
        super().__init__(*args, **kwargs)
        self._logger = None
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger instance for this class."""
        if self._logger is None:
            self._logger = get_logger(self.__class__.__name__)
        return self._logger
    
    def log_debug(self, message: str, **kwargs) -> None:
        """Log debug message."""
        self.logger.debug(message, extra=kwargs)
    
    def log_info(self, message: str, **kwargs) -> None:
        """Log info message."""
        self.logger.info(message, extra=kwargs)
    
    def log_warning(self, message: str, **kwargs) -> None:
        """Log warning message."""
        self.logger.warning(message, extra=kwargs)
    
    def log_error(self, message: str, **kwargs) -> None:
        """Log error message."""
        self.logger.error(message, extra=kwargs)
    
    def log_critical(self, message: str, **kwargs) -> None:
        """Log critical message."""
        self.logger.critical(message, extra=kwargs)


def get_logger(name: str, level: Optional[str] = None) -> logging.Logger:
    """
    Get a configured logger instance.
    
    Args:
        name: Logger name
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(f"quizaigen.{name}")
    
    # Don't add handlers if already configured
    if logger.handlers:
        return logger
    
    # Set level
    if level is None:
        level = "INFO"
    
    logger.setLevel(getattr(logging, level.upper()))
    
    # Create console handler
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(getattr(logging, level.upper()))
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    handler.setFormatter(formatter)
    
    # Add handler to logger
    logger.addHandler(handler)
    
    # Prevent propagation to root logger
    logger.propagate = False
    
    return logger


def configure_logging(
    level: str = "INFO",
    log_file: Optional[str] = None,
    format_string: Optional[str] = None
) -> None:
    """
    Configure global logging settings for QuizAIGen.
    
    Args:
        level: Global log level
        log_file: Optional log file path
        format_string: Optional custom format string
    """
    # Set root logger level
    root_logger = logging.getLogger("quizaigen")
    root_logger.setLevel(getattr(logging, level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Default format
    if format_string is None:
        format_string = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    formatter = logging.Formatter(format_string, datefmt='%Y-%m-%d %H:%M:%S')
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, level.upper()))
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler if specified
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(getattr(logging, level.upper()))
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # Prevent propagation to root
    root_logger.propagate = False


def get_logging_config() -> Dict[str, Any]:
    """
    Get current logging configuration.
    
    Returns:
        Dictionary with logging configuration
    """
    root_logger = logging.getLogger("quizaigen")
    
    return {
        "level": logging.getLevelName(root_logger.level),
        "handlers": [
            {
                "type": type(handler).__name__,
                "level": logging.getLevelName(handler.level),
                "formatter": str(handler.formatter._fmt) if handler.formatter else None
            }
            for handler in root_logger.handlers
        ],
        "propagate": root_logger.propagate
    }


def disable_logging() -> None:
    """Disable all QuizAIGen logging."""
    logging.getLogger("quizaigen").disabled = True


def enable_logging() -> None:
    """Re-enable QuizAIGen logging."""
    logging.getLogger("quizaigen").disabled = False


# Default configuration
configure_logging(level="WARNING")  # Default to WARNING to reduce noise
